# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Building the Application

```bash
# Build debug APK
./gradlew assembleDebug

# Build release APK
./gradlew assembleRelease

# Clean build
./gradlew clean

# Install debug APK to connected device/emulator
adb install -r app/build/outputs/apk/debug/app-debug.apk
```

### Development Environment Setup

Ensure Java 17 is configured:
```bash
# Ubuntu/WSL
sudo apt install openjdk-17-jdk
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
```

## Architecture

### WHIP Streamer Android Application

This is a WebRTC live streaming application supporting WHIP (WebRTC-HTTP Ingestion Protocol) and SRS WebRTC protocols, optimized for Android emulators like MuMu Player 12.

#### Core Components

1. **MainActivity.kt** - Main activity containing WebRTC initialization and streaming logic
   - Handles camera and microphone permissions
   - Manages WebRTC PeerConnection lifecycle
   - Implements dual protocol support (WHIP and SRS WebRTC)
   - Provides camera switching functionality

2. **PeerConnectionObserver.kt** - Base observer class for WebRTC PeerConnection events
   - Compatible with WebRTC SDK 104.5112.09
   - Implements all required interface methods with default empty implementations

3. **LogUtil.kt** - Logging utility that automatically disables logs in release builds
   - Uses BuildConfig.DEBUG to control log output
   - Provides formatted logging and stack trace functionality

#### Key Technical Features

**Smart Encoder Detection and Fallback**:
- Hardware H.264 encoder detection for real devices
- Software H.264 encoder fallback for emulators
- Automatic emulator detection (MuMu Player, BlueStacks, etc.)

**Dual Protocol Support**:
- **WHIP Protocol**: HTTP POST with SDP to `/rtc/v1/whip/` endpoint
- **SRS WebRTC**: JSON signaling to `/rtc/v1/publish/` endpoint

**Camera Management**:
- Support for both Camera1 and Camera2 APIs
- Dynamic video parameter selection based on camera capabilities
- Front/rear camera switching with live preview

#### Server Configuration

Default server: `**************`

- **WHIP URL**: `http://**************:1985/rtc/v1/whip/?app=live&stream=livestream`
- **SRS WebRTC URL**: `webrtc://**************/live/livestream`
- **Signaling URL**: `http://**************:1985/rtc/v1/publish/`

#### Dependencies

Key dependencies defined in `app/build.gradle`:
- WebRTC SDK: `io.github.webrtc-sdk:android:104.5112.09`
- OkHttp: `com.squareup.okhttp3:okhttp:4.10.0`
- Kotlin Coroutines: `org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4`

#### Build Configuration

- **Target SDK**: 34 (Android 14)
- **Min SDK**: 21 (Android 5.0)
- **Kotlin JVM Target**: 11
- **Java Compatibility**: 11
- **Gradle**: 8.11.1 with performance optimizations in `gradle.properties`

### Emulator vs Real Device Behavior

**Real Devices**:
- Use hardware H.264 encoders for optimal performance
- Full WebRTC feature support with low latency

**Emulators**:
- Automatic detection and software H.264 encoder fallback
- Special handling for MuMu Player 12 environment
- Maintains streaming functionality with software encoding

### Streaming Workflow

1. Initialize WebRTC with EglBase context
2. Create video/audio tracks from camera and microphone
3. Establish PeerConnection with ICE servers
4. Generate SDP offer and send via selected protocol
5. Handle server response and establish media connection
6. Monitor connection state via PeerConnectionObserver

### UI State Management

- Real-time connection status updates
- Button state management (start/stop/switch camera)
- Local video preview with mirror mode for front camera
- Protocol selection (WHIP vs SRS WebRTC) with URL auto-switching