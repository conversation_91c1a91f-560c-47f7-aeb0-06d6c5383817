pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven { url 'https://maven.google.com' }
        // For WebRTC
        maven { url 'https://oss.sonatype.org/content/repositories/snapshots' }
    }
}
rootProject.name = "WhipStreamer"
include ':app'
