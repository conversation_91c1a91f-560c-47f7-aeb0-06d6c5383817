<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 夜间模式应用主题 -->
    <style name="Theme.WhipStreamer" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- 主要颜色属性 -->
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- 次要颜色属性 -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- 状态栏颜色 -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
    </style>
</resources>
