# WHIP Streamer Android Project

## 项目概述

这是一个支持WHIP协议和SRS WebRTC的Android直播推流应用，专门针对虚拟机环境（如MuMu Player 12）进行了优化，实现了智能编码器检测和自动fallback机制。

## 主要功能

- ✅ **WHIP协议推流** - 支持WebRTC-HTTP Ingestion Protocol
- ✅ **SRS WebRTC推流** - 兼容SRS媒体服务器的WebRTC协议
- ✅ **智能编码器检测** - 自动检测H.264硬件编码器支持
- ✅ **软件编码器Fallback** - 在虚拟机中自动使用软件H.264编码器
- ✅ **摄像头支持** - 支持前后摄像头切换
- ✅ **后台推流** - 支持应用切换到后台继续推流

## 技术架构

### 核心组件

1. **MainActivity.kt** - 主要活动，包含WebRTC初始化和推流逻辑
2. **StreamingService.kt** - 前台服务，支持后台推流
3. **PeerConnectionObserver.kt** - WebRTC连接状态监听器
4. **LogUtil.kt** - 日志工具类

### 关键特性

#### 智能编码器选择
```kotlin
private fun createAdaptiveEncoderFactory(): VideoEncoderFactory {
    val hasH264Hardware = checkH264HardwareSupport()
    // 优先使用硬件编码器，失败时自动fallback到软件编码器
}
```

#### 模拟器检测
```kotlin
private fun isEmulator(): Boolean {
    // 检测MuMu Player、BlueStacks等各种Android模拟器
    // 支持MuMu Player 12特殊检测
}
```

#### H.264硬件支持检测
```kotlin
private fun checkH264HardwareSupport(): Boolean {
    // 扫描系统MediaCodecList查找H.264硬件编码器
    // 确保编码器可用性
}
```

## 服务器配置

### SRS服务器设置

项目配置为连接到SRS服务器：`**************`

#### WHIP配置
- **URL**: `http://**************:1985/rtc/v1/whip/?app=live&stream=livestream`
- **协议**: HTTP POST with SDP
- **Content-Type**: `application/sdp`

#### SRS WebRTC配置
- **信令URL**: `http://**************:1985/rtc/v1/publish/`
- **流地址**: `webrtc://**************/live/livestream`

### SRS配置文件示例
```conf
# srs.conf
rtc_server {
    enabled on;
    listen 8000;
}

http_api {
    enabled on;
    listen 1985;
}

http_server {
    enabled on;
    listen 1985;
    dir ./objs/nginx/html;
}
```

## 环境兼容性

### 真实设备
- ✅ 使用硬件H.264编码器（最佳性能）
- ✅ 完整的WebRTC功能支持
- ✅ 低延迟、高画质

### 虚拟机环境
- ✅ **MuMu Player 12** - 自动使用软件H.264编码器
- ✅ **BlueStacks** - 支持检测和适配
- ✅ **其他模拟器** - 通用模拟器检测机制

## 编译要求

### 开发环境
- **Android Studio**: Arctic Fox 2020.3.1+
- **Java**: JDK 17+
- **Gradle**: 8.11.1
- **Android API**: 21+ (最低) / 34 (目标)

### 依赖库
```gradle
dependencies {
    implementation 'org.webrtc:google-webrtc:1.0.32006'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    // ... 其他依赖
}
```

## 安装与使用

### 1. 克隆项目
```bash
cd /your/project/path
```

### 2. 配置环境
确保安装了Java 17:
```bash
# Ubuntu/WSL
sudo apt install openjdk-17-jdk
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
```

### 3. 编译应用
```bash
./gradlew assembleDebug
```

### 4. 安装到设备
```bash
# 安装到连接的Android设备/模拟器
adb install -r app/build/outputs/apk/debug/app-debug.apk
```

### 5. 运行测试
1. 打开应用
2. 选择WHIP或SRS WebRTC协议
3. 点击开始推流
4. 查看Logcat日志确认编码器选择

## 关键日志

### 模拟器环境日志
```
WhipStreamer: 设备信息检测: brand=generic, device=generic_x86_64, model=MuMu
WhipStreamer: 模拟器检测结果: true
WhipStreamer: H.264硬件编码器支持: false
WhipStreamer: 编码器工厂支持的编解码器:
WhipStreamer:   H264: {level-asymmetry-allowed=1, packetization-mode=1, profile-level-id=42e01f}
WhipStreamer: 请求创建编码器: H264
WhipStreamer: 使用H.264软件编码器
```

### 真实设备日志
```
WhipStreamer: 设备信息检测: brand=samsung, device=SM-G973F, model=Galaxy S10
WhipStreamer: 模拟器检测结果: false
WhipStreamer: H.264硬件编码器支持: true
WhipStreamer: 请求创建编码器: H264
WhipStreamer: H.264硬件编码器创建成功
```

## 问题排查

### 常见问题

#### 1. WHIP连接失败
**症状**: `Empty reply from server` 或连接超时
**解决方案**:
- 检查SRS服务器状态: `curl http://**************:1985/api/v1/versions`
- 确认网络连通性
- 尝试切换到SRS WebRTC协议

#### 2. 虚拟机无H.264支持
**症状**: SDP内容长度较短，缺少H.264编解码器
**解决方案**:
- 确认应用检测到模拟器环境
- 查看日志确认使用软件H.264编码器
- 如果仍无效，检查MuMu Player 12设置中的硬件加速选项

#### 3. Java版本不兼容
**症状**: `Android Gradle plugin requires Java 17`
**解决方案**:
```bash
sudo apt install openjdk-17-jdk
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
./gradlew --stop
./gradlew assembleDebug
```

#### 4. 编译错误
**症状**: `MaxPermSize` 不支持
**解决方案**: 已在gradle.properties中移除过时的JVM参数

## 网络监控

项目包含网络监控脚本用于调试连接问题：

### monitor_whip_connections.sh
```bash
# 同时监控SRS日志、网络包和HTTP请求
./monitor_whip_connections.sh
```

监控内容：
- SRS服务器日志
- 网络包捕获(1985端口)
- HTTP请求详情
- WebRTC端口监控(8000端口)
- 系统资源使用

## 升级历史

### v1.0 (当前版本)
- ✅ 支持WHIP和SRS WebRTC双协议
- ✅ 智能编码器检测和fallback
- ✅ 完整的虚拟机适配
- ✅ 网络监控和调试工具

### 已解决的问题
1. **SRS 5.0.213 → 6.0.166升级** - 添加WHIP协议支持
2. **虚拟机H.264编码器缺失** - 实现软件编码器fallback
3. **网络连接调试困难** - 添加详细的网络监控
4. **Java 17兼容性** - 更新构建配置

## 开发团队

- **架构设计**: 基于WebRTC标准和WHIP协议规范
- **虚拟机适配**: 针对MuMu Player 12等主流Android模拟器
- **网络优化**: 针对国内网络环境的STUN服务器配置

## 许可证

本项目仅供学习和开发使用。

## 相关文档

- [WHIP协议规范](https://datatracker.ietf.org/doc/draft-ietf-wish-whip/)
- [SRS官方文档](https://ossrs.net/)
- [WebRTC开发指南](https://webrtc.org/)
- [Android模拟器开发最佳实践](https://developer.android.com/studio/run/emulator)

---

**最后更新**: 2025年6月10日
**版本**: v1.0
**兼容性**: Android 5.0+ (API 21+)