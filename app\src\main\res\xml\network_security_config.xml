<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <!-- 允许对SRS服务器的HTTP明文传输 -->
        <domain includeSubdomains="true">**************</domain>
        <!-- 允许本地开发服务器 -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">********</domain>
    </domain-config>
    
    <!-- 在开发阶段允许所有HTTP连接 -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>