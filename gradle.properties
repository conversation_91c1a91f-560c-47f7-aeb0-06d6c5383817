# 项目级 gradle.properties

# Gradle 性能优化
org.gradle.jvmargs=-Xmx4096m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true
# android.enableBuildCache 已在 Gradle 7.0 中被移除

# 编译加速
kapt.incremental.apt=true
kapt.use.worker.api=true
kapt.include.compile.classpath=false

# AndroidX 配置
android.useAndroidX=true
android.enableJetifier=true

# Kotlin 代码风格
kotlin.code.style=official

# 启用 R8 完全模式
android.enableR8.fullMode=true

# 编译警告处理
android.debug.obsoleteApi=false
# android.defaults.buildfeatures.buildconfig=true # 已弃用的设置
android.nonTransitiveRClass=false
android.nonFinalResIds=false
