{"permissions": {"allow": ["Bash(grep:*)", "<PERSON><PERSON>(./gradlew:*)", "Bash(find:*)", "Bash(rm:*)", "<PERSON><PERSON>(chmod:*)", "WebFetch(domain:github.com)", "Bash(\"/mnt/c/Users/<USER>/Desktop/SRS-CentOS7-x86_64-6.0-a2/usr/local/srs/objs/srs\" -version)", "WebFetch(domain:ossrs.net)", "<PERSON><PERSON>(sudo apt:*)", "Bash(export:*)", "Bash(JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64 ./gradlew assembleDebug)", "Bash(rg:*)", "Bash(ls:*)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 10 -B 2 \"circuitBreaker|CIRCUIT_BREAKER\" /mnt/c/Users/<USER>/Desktop/project/app/src/main/java/com/example/whipstreamer/MainActivity.kt)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 15 -B 5 \"encoderCreationCount\" /mnt/c/Users/<USER>/Desktop/project/app/src/main/java/com/example/whipstreamer/MainActivity.kt)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 5 -B 5 \"circuitBreakerTripped.*=\" /mnt/c/Users/<USER>/Desktop/project/app/src/main/java/com/example/whipstreamer/MainActivity.kt)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 5 -B 5 \"circuit|breaker|熔断|state\" /mnt/c/Users/<USER>/Desktop/project/app/src/main/java/com/example/whipstreamer/PeerConnectionObserver.kt)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 10 -B 5 \"紧急停止|emergency\" /mnt/c/Users/<USER>/Desktop/project/app/src/main/java/com/example/whipstreamer/MainActivity.kt)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 15 -B 5 \"MAX_HARDWARE_FAILURES|硬件编码器连续失败\" /mnt/c/Users/<USER>/Desktop/project/app/src/main/java/com/example/whipstreamer/MainActivity.kt)", "Bash(/home/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 3 -B 3 \"MAX_HARDWARE_FAILURES\" /mnt/c/Users/<USER>/Desktop/project/app/src/main/java/com/example/whipstreamer/MainActivity.kt)", "Bash(cp:*)", "<PERSON><PERSON>(env)"], "deny": []}}