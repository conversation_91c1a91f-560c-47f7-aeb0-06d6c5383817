<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <FrameLayout
        android:id="@+id/videoContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@android:color/black"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/controlPanel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">
        
        <org.webrtc.SurfaceViewRenderer
            android:id="@+id/localVideoView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:adjustViewBounds="true" />
    </FrameLayout>

    <LinearLayout
        android:id="@+id/controlPanel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/serverUrlInputLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/serverUrlInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="服务器 URL"
                android:inputType="textUri"
                android:text="http://**************:1985/rtc/v1/whip/?app=live&amp;stream=livestream" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/streamKeyInputLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/streamKeyInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="流密钥"
                android:inputType="text"
                android:text="livestream" />
        </com.google.android.material.textfield.TextInputLayout>

        <RadioGroup
            android:id="@+id/protocolSelector"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <RadioButton
                android:id="@+id/whipRadio"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="WHIP"
                android:checked="true"
                android:layout_marginEnd="16dp" />

            <RadioButton
                android:id="@+id/webrtcRadio"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="SRS WebRTC" />
        </RadioGroup>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/startButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="开始推流"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/stopButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="停止推流"
                android:enabled="false"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/switchCameraButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="切换摄像头"
                android:enabled="true" />
        </LinearLayout>

        <TextView
            android:id="@+id/statusText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="状态: 未连接"
            android:textSize="14sp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
