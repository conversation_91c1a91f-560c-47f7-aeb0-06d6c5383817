package com.example.whipstreamer

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import android.view.WindowManager
import androidx.lifecycle.lifecycleScope
import com.example.whipstreamer.databinding.ActivityMainBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.webrtc.*
import org.webrtc.PeerConnection.*
import org.webrtc.RendererCommon
import java.util.concurrent.TimeUnit
import org.json.JSONObject
import org.webrtc.CameraEnumerationAndroid
import android.util.Log

class MainActivity : AppCompatActivity() {
    private lateinit var binding: ActivityMainBinding
    
    companion object {
        private const val TAG = "WhipStreamer"
        private const val PERMISSION_REQUEST_CODE = 1001
        private val REQUIRED_PERMISSIONS = arrayOf(
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO
        )
    }
    
    // WebRTC相关
    private var peerConnectionFactory: PeerConnectionFactory? = null
    private var peerConnection: PeerConnection? = null
    private var localVideoTrack: VideoTrack? = null
    private var localAudioTrack: AudioTrack? = null
    private var videoCapturer: VideoCapturer? = null
    private var eglBase: EglBase? = null
    private var cameraEnumerator: CameraEnumerator? = null
    
    // 流状态
    private var isStreaming = false
    private var isFrontCamera = true

    // ICE候选项统计
    private var iceGatheringStartTime = 0L
    private var totalCandidatesCollected = 0
    private var hostCandidates = 0
    private var stunCandidates = 0
    private var turnCandidates = 0
    private var otherCandidates = 0
    private var offerSent = false
    
    // 网络客户端
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(10, TimeUnit.SECONDS)
        .writeTimeout(10, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .build()
    
    // 默认配置 - 指向WHIP服务器
    private val DEFAULT_WHIP_URL = "http://**************:8090/whip"
    private val DEFAULT_SRS_WEBRTC_URL = "webrtc://**************/live/livestream"
    
    // 视频参数 - 网络自适应最小参数
    private var VIDEO_WIDTH = 1280        // 起始分辨率
    private var VIDEO_HEIGHT = 720        // 起始分辨率
    private var VIDEO_FPS = 30           // 起始帧率

    // 自适应最小参数 - 降低下限提升适应性
    private val MIN_VIDEO_WIDTH = 854     // 最小保持480P
    private val MIN_VIDEO_HEIGHT = 480    // 最小保持480P
    private val MIN_VIDEO_FPS = 15        // 最小保持15帧

    // 缓冲区配置 - 与服务器端匹配的低延迟设置
    private val AUDIO_BUFFER_FRAMES = 16         // 16帧音频缓冲区 (约333ms@48kHz)
    private val VIDEO_BUFFER_FRAMES = 32         // 32帧视频缓冲区 (约1.07s@30fps)
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // 防止屏幕息屏
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        
        setupUI()
        
        // 检查并请求权限
        if (!hasPermissions()) {
            requestPermissions()
        } else {
            initWebRTC()
        }
    }
    
    private fun setupUI() {
        // 设置默认 URL
        binding.serverUrlInput.setText(DEFAULT_WHIP_URL)
        
        // 初始按钮状态
        binding.startButton.isEnabled = true
        binding.stopButton.isEnabled = false
        
        // 协议选择
        binding.whipRadio.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.serverUrlInput.setText(DEFAULT_WHIP_URL)
            }
        }
        
        binding.webrtcRadio.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                binding.serverUrlInput.setText(DEFAULT_SRS_WEBRTC_URL)
            }
        }
        
        // 按钮事件
        binding.startButton.setOnClickListener {
            if (!isStreaming) {
                startStreaming()
            }
        }
        
        binding.stopButton.setOnClickListener {
            if (isStreaming) {
                stopStreaming()
            }
        }
        
        binding.switchCameraButton.setOnClickListener {
            switchCamera()
        }
    }
    
    private fun hasPermissions(): Boolean {
        return REQUIRED_PERMISSIONS.all {
            ContextCompat.checkSelfPermission(this, it) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    private fun requestPermissions() {
        ActivityCompat.requestPermissions(this, REQUIRED_PERMISSIONS, PERMISSION_REQUEST_CODE)
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                initWebRTC()
            } else {
                Toast.makeText(this, "需要摄像头和麦克风权限才能使用此应用", Toast.LENGTH_LONG).show()
                finish()
            }
        }
    }
    
    private fun initWebRTC() {
        try {
            // 初始化 EglBase
            eglBase = EglBase.create()
            
            // 初始化本地视频显示
            binding.localVideoView.init(eglBase?.eglBaseContext, null)
            binding.localVideoView.setEnableHardwareScaler(true)
            binding.localVideoView.setMirror(true)
            binding.localVideoView.setScalingType(RendererCommon.ScalingType.SCALE_ASPECT_BALANCED)
            
            // 初始化 PeerConnectionFactory
            val options = PeerConnectionFactory.InitializationOptions.builder(this)
                .setEnableInternalTracer(false)
                .createInitializationOptions()
            PeerConnectionFactory.initialize(options)
            
            // 创建编码器工厂 - 硬件优先，软件备选
            val encoderFactory = createSimpleEncoderFactory()
            val decoderFactory = DefaultVideoDecoderFactory(eglBase?.eglBaseContext)
            
            peerConnectionFactory = PeerConnectionFactory.builder()
                .setVideoEncoderFactory(encoderFactory)
                .setVideoDecoderFactory(decoderFactory)
                .createPeerConnectionFactory()
            
            // 创建摄像头捕获器
            videoCapturer = createCameraCapturer()
            if (videoCapturer == null) {
                Toast.makeText(this, "无法访问摄像头", Toast.LENGTH_LONG).show()
                return
            }
            
            // 创建视频轨道
            createVideoTrack()
            // 创建音频轨道
            createAudioTrack()

            Log.d(TAG, "WebRTC 初始化完成")
            Log.d(TAG, "📦 缓冲区配置:")
            Log.d(TAG, "   🎵 音频缓冲: ${AUDIO_BUFFER_FRAMES}帧 (约${AUDIO_BUFFER_FRAMES * 1000 / 48}ms@48kHz)")
            Log.d(TAG, "   📹 视频缓冲: ${VIDEO_BUFFER_FRAMES}帧 (约${VIDEO_BUFFER_FRAMES * 1000 / 30}ms@30fps)")
            Log.d(TAG, "🌐 WebRTC自适应算法配置:")
            Log.d(TAG, "   🚦 GCC: Google拥塞控制算法")
            Log.d(TAG, "   📊 BWE: 带宽估计算法")
            Log.d(TAG, "   🔄 ABC: 自适应码率控制")
            Log.d(TAG, "   📡 REMB: 接收端最大码率估计")
            Log.d(TAG, "   📺 起始分辨率: ${VIDEO_WIDTH}x${VIDEO_HEIGHT}@${VIDEO_FPS}fps")
            Log.d(TAG, "   📊 最小保持: ${MIN_VIDEO_WIDTH}x${MIN_VIDEO_HEIGHT}@${MIN_VIDEO_FPS}fps (480P)")
            
        } catch (e: Exception) {
            Log.e(TAG, "WebRTC 初始化失败", e)
            Toast.makeText(this, "WebRTC 初始化失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
    
    private fun createSimpleEncoderFactory(): VideoEncoderFactory {
        // 使用DefaultVideoEncoderFactory，这是最兼容的选择
        return DefaultVideoEncoderFactory(
            eglBase?.eglBaseContext,
            false, // enableIntelVp8Encoder
            true   // enableH264HighProfile
        )
    }
    
    private fun createCameraCapturer(): VideoCapturer? {
        cameraEnumerator = if (Camera2Enumerator.isSupported(this)) {
            Camera2Enumerator(this)
        } else {
            Camera1Enumerator(true)
        }
        
        val enumerator = cameraEnumerator!!
        val deviceNames = enumerator.deviceNames
        
        // 优先使用前置摄像头
        for (deviceName in deviceNames) {
            if (enumerator.isFrontFacing(deviceName)) {
                updateVideoParameters(deviceName)
                val capturer = enumerator.createCapturer(deviceName, null)
                if (capturer != null) {
                    isFrontCamera = true
                    return capturer
                }
            }
        }
        
        // 如果没有前置，使用后置摄像头
        for (deviceName in deviceNames) {
            if (!enumerator.isFrontFacing(deviceName)) {
                updateVideoParameters(deviceName)
                val capturer = enumerator.createCapturer(deviceName, null)
                if (capturer != null) {
                    isFrontCamera = false
                    return capturer
                }
            }
        }
        
        return null
    }
    
    private fun updateVideoParameters(deviceName: String) {
        val formats = cameraEnumerator?.getSupportedFormats(deviceName)
        if (formats != null && formats.isNotEmpty()) {
            // 选择最佳格式
            val bestFormat = selectBestFormat(formats)
            VIDEO_WIDTH = bestFormat.width
            VIDEO_HEIGHT = bestFormat.height
            VIDEO_FPS = bestFormat.framerate.max / 1000
            Log.d(TAG, "相机参数: ${VIDEO_WIDTH}x${VIDEO_HEIGHT}@${VIDEO_FPS}fps")
        }
    }
    
    private fun selectBestFormat(formats: List<CameraEnumerationAndroid.CaptureFormat>): CameraEnumerationAndroid.CaptureFormat {
        // 优先选择720p 30fps
        formats.find { 
            it.width == 1280 && it.height == 720 && it.framerate.max >= 30000 
        }?.let { return it }
        
        // 其次选择接近16:9的格式
        val aspect16_9Formats = formats.filter { format ->
            val aspectRatio = format.width.toFloat() / format.height.toFloat()
            Math.abs(aspectRatio - 16f/9f) < 0.1f && format.framerate.max >= 25000
        }
        
        return aspect16_9Formats.minByOrNull { 
            Math.abs(it.width * it.height - 1280 * 720) 
        } ?: formats.first()
    }
    
    private fun createVideoTrack() {
        // 创建视频源，启用低延迟模式
        val videoSource = peerConnectionFactory?.createVideoSource(false)
        videoCapturer?.initialize(
            SurfaceTextureHelper.create("CameraThread", eglBase?.eglBaseContext),
            this,
            videoSource?.capturerObserver
        )

        localVideoTrack = peerConnectionFactory?.createVideoTrack("video_track", videoSource)
        localVideoTrack?.addSink(binding.localVideoView)

        // 启动摄像头
        videoCapturer?.startCapture(VIDEO_WIDTH, VIDEO_HEIGHT, VIDEO_FPS)

        Log.d(TAG, "📹 视频轨道创建完成 - 缓冲区: ${VIDEO_BUFFER_FRAMES}帧")
    }
    
    private fun createAudioTrack() {
        val audioConstraints = MediaConstraints().apply {
            // 基础音频增强
            mandatory.add(MediaConstraints.KeyValuePair("googEchoCancellation", "true"))
            mandatory.add(MediaConstraints.KeyValuePair("googNoiseSuppression", "true"))
            mandatory.add(MediaConstraints.KeyValuePair("googAutoGainControl", "true"))
            mandatory.add(MediaConstraints.KeyValuePair("googHighpassFilter", "true"))

            // 音频自适应配置 - 支持BWE和GCC
            optional.add(MediaConstraints.KeyValuePair("googAudioNetworkAdaptation", "true"))
            optional.add(MediaConstraints.KeyValuePair("googAudioLatency", "low"))
            optional.add(MediaConstraints.KeyValuePair("googAudioBitrate", "128000"))      // 128kbps音频码率
            optional.add(MediaConstraints.KeyValuePair("googAudioAdaptiveBitrate", "true")) // 音频自适应码率
            optional.add(MediaConstraints.KeyValuePair("googAudioCongestionControl", "true")) // 音频拥塞控制
        }

        val audioSource = peerConnectionFactory?.createAudioSource(audioConstraints)
        localAudioTrack = peerConnectionFactory?.createAudioTrack("audio_track", audioSource)

        // 检查音频轨道状态
        localAudioTrack?.let { track ->
            track.setEnabled(true)
            Log.d(TAG, "🎵 音频轨道创建完成:")
            Log.d(TAG, "   📦 缓冲区: ${AUDIO_BUFFER_FRAMES}帧")
            Log.d(TAG, "   🔊 轨道状态: enabled=${track.enabled()}, kind=${track.kind()}")
            Log.d(TAG, "   🎤 轨道ID: ${track.id()}")
        } ?: run {
            Log.e(TAG, "❌ 音频轨道创建失败！")
        }
    }
    
    private fun createPeerConnection(): PeerConnection? {
        // 简化ICE服务器配置 - 只保留核心服务器
        val iceServers = listOf(
            // 主要STUN服务器
            IceServer.builder("stun:stun.cloudflare.com:3478").createIceServer(),
            // TURN服务器作为备选
            IceServer.builder("turn:**************:3478")
                .setUsername("a26546593")
                .setPassword("2179365a")
                .createIceServer()
        )
        
        // RTC配置 - 启用所有自适应算法
        val rtcConfig = RTCConfiguration(iceServers).apply {
            bundlePolicy = BundlePolicy.MAXBUNDLE
            rtcpMuxPolicy = RtcpMuxPolicy.REQUIRE
            // ICE策略
            continualGatheringPolicy = ContinualGatheringPolicy.GATHER_ONCE
            // 标准超时配置
            iceConnectionReceivingTimeout = 30000
            iceBackupCandidatePairPingInterval = 5000

        }
        
        val peerConnectionObserver = object : PeerConnection.Observer {
            override fun onSignalingChange(state: SignalingState?) {
                Log.d(TAG, "信令状态变化: $state")
            }
            
            override fun onIceConnectionChange(state: IceConnectionState?) {
                Log.d(TAG, "🧊 ICE连接状态: $state")
                runOnUiThread {
                    when (state) {
                        IceConnectionState.CHECKING -> {
                            binding.statusText.text = "状态: 正在连接..."
                            binding.startButton.isEnabled = false
                            binding.stopButton.isEnabled = true
                        }
                        IceConnectionState.CONNECTED, IceConnectionState.COMPLETED -> {
                            binding.statusText.text = "状态: 推流成功 (GCC+BWE+REMB)"
                            isStreaming = true
                            binding.startButton.isEnabled = false
                            binding.stopButton.isEnabled = true
                            Log.d(TAG, "✅ ICE连接成功 - WebRTC核心自适应算法已启用")
                            Log.d(TAG, "🚦 GCC拥塞控制: 实时调整发送速率")
                            Log.d(TAG, "📊 BWE带宽估计: 动态评估网络容量")
                            Log.d(TAG, "📡 REMB反馈: 接收端码率控制")
                            Log.d(TAG, "🌐 自适应范围: ${MIN_VIDEO_WIDTH}x${MIN_VIDEO_HEIGHT}@${MIN_VIDEO_FPS}fps ~ ${VIDEO_WIDTH}x${VIDEO_HEIGHT}@${VIDEO_FPS}fps")
                            Log.d(TAG, "📦 当前缓冲区: 视频${VIDEO_BUFFER_FRAMES}帧, 音频${AUDIO_BUFFER_FRAMES}帧")
                        }
                        IceConnectionState.DISCONNECTED, IceConnectionState.FAILED -> {
                            binding.statusText.text = "状态: 连接失败"
                            isStreaming = false
                            binding.startButton.isEnabled = true
                            binding.stopButton.isEnabled = false
                            Log.w(TAG, "⚠️ ICE连接失败: $state")
                        }
                        IceConnectionState.CLOSED -> {
                            binding.statusText.text = "状态: 连接已关闭"
                        }
                        else -> {
                            // 忽略其他状态
                        }
                    }
                }
            }
            
            override fun onIceGatheringChange(state: IceGatheringState?) {
                Log.d(TAG, "🧊 ICE收集状态: $state")
                when (state) {
                    IceGatheringState.NEW -> {
                        iceGatheringStartTime = System.currentTimeMillis()
                    }
                    IceGatheringState.GATHERING -> {
                        Log.d(TAG, "🔍 正在收集ICE候选项...")
                    }
                    IceGatheringState.COMPLETE -> {
                        val duration = System.currentTimeMillis() - iceGatheringStartTime
                        Log.d(TAG, "✅ ICE收集完成 - 耗时${duration}ms, 候选项${totalCandidatesCollected}个")

                        if (totalCandidatesCollected > 0) {
                            sendOfferWithCandidates()
                        } else {
                            Log.e(TAG, "❌ 没有收集到ICE候选项")
                        }
                    }
                    else -> {
                        // 忽略其他状态
                    }
                }
            }

            override fun onIceCandidate(candidate: IceCandidate?) {
                candidate?.let {
                    totalCandidatesCollected++

                    // 简化候选项类型统计
                    when {
                        it.sdp.contains("typ host") -> hostCandidates++
                        it.sdp.contains("typ srflx") -> stunCandidates++
                        it.sdp.contains("typ relay") -> turnCandidates++
                        else -> otherCandidates++
                    }

                    Log.d(TAG, "📡 ICE候选项 #$totalCandidatesCollected")
                }
            }

            override fun onIceCandidatesRemoved(candidates: Array<out IceCandidate>?) {
                Log.d(TAG, "🗑️ ICE候选项被移除: ${candidates?.size ?: 0}个")
            }
            override fun onAddStream(stream: MediaStream?) {}
            override fun onRemoveStream(stream: MediaStream?) {}
            override fun onDataChannel(channel: DataChannel?) {}
            override fun onRenegotiationNeeded() {}
            override fun onAddTrack(receiver: RtpReceiver?, streams: Array<out MediaStream>?) {}
            
            // 必需的方法 - WebRTC SDK要求
            override fun onIceConnectionReceivingChange(p0: Boolean) {}
            override fun onConnectionChange(newState: PeerConnection.PeerConnectionState?) {}
            override fun onSelectedCandidatePairChanged(event: CandidatePairChangeEvent?) {}
        }
        
        return peerConnectionFactory?.createPeerConnection(rtcConfig, peerConnectionObserver)
    }
    
    private fun startStreaming() {
        if (isStreaming) return

        Log.d(TAG, "开始推流...")

        // 重置ICE候选项统计
        resetIceStatistics()

        // 更新UI状态
        runOnUiThread {
            binding.statusText.text = "状态: 正在连接..."
            binding.startButton.isEnabled = false
            binding.stopButton.isEnabled = true
        }
        
        // 创建 PeerConnection
        peerConnection = createPeerConnection()
        if (peerConnection == null) {
            runOnUiThread {
                Toast.makeText(this, "创建连接失败", Toast.LENGTH_SHORT).show()
                binding.statusText.text = "状态: 连接创建失败"
                binding.startButton.isEnabled = true
                binding.stopButton.isEnabled = false
            }
            return
        }
        
        // 添加轨道到PeerConnection进行网络推流
        localVideoTrack?.let { 
            peerConnection?.addTrack(it, listOf("stream"))
            Log.d(TAG, "视频轨道已添加到PeerConnection，本地预览保持不变")
        }
        localAudioTrack?.let { audioTrack ->
            val sender = peerConnection?.addTrack(audioTrack, listOf("stream"))
            Log.d(TAG, "🎵 音频轨道已添加到PeerConnection:")
            Log.d(TAG, "   🔊 轨道状态: enabled=${audioTrack.enabled()}")
            Log.d(TAG, "   📡 发送器: ${sender?.id()}")
        } ?: run {
            Log.e(TAG, "❌ 音频轨道为空，无法添加到PeerConnection")
        }
        
        // 简化推流启动 - 直接创建offer
        Log.d(TAG, "🚀 创建offer SDP...")

        // 短暂延迟让轨道准备就绪
        Handler(Looper.getMainLooper()).postDelayed({
            createOfferWithDelay()
        }, 1000) // 1秒延迟足够
    }

    private fun createOfferWithDelay() {
        val mediaConstraints = MediaConstraints().apply {
            // 基础SDP配置
            mandatory.add(MediaConstraints.KeyValuePair("OfferToReceiveAudio", "false"))
            mandatory.add(MediaConstraints.KeyValuePair("OfferToReceiveVideo", "false"))

            // === GCC (Google Congestion Control) - 优化高带宽场景 ===
            optional.add(MediaConstraints.KeyValuePair("googCongestionControl", "true"))
            optional.add(MediaConstraints.KeyValuePair("googTransportCc", "true"))        // Transport-cc扩展
            optional.add(MediaConstraints.KeyValuePair("googCongestionWindow", "true"))   // 拥塞窗口控制
            optional.add(MediaConstraints.KeyValuePair("googCongestionProbing", "true"))  // 主动拥塞探测

            // === BWE (Bandwidth Estimation) - 激进带宽探测 ===
            optional.add(MediaConstraints.KeyValuePair("googAdaptiveBwe", "true"))        // 自适应带宽估计
            optional.add(MediaConstraints.KeyValuePair("googBweProbing", "true"))         // 带宽探测
            optional.add(MediaConstraints.KeyValuePair("googNetworkAdaptation", "true"))  // 网络自适应
            optional.add(MediaConstraints.KeyValuePair("googBweRampUpRate", "fast"))      // 快速带宽上升
            optional.add(MediaConstraints.KeyValuePair("googBweBackoffFactor", "0.8"))    // 降级因子(更保守)

            // === Adaptive Bitrate Control ===
            optional.add(MediaConstraints.KeyValuePair("googAdaptiveFramerate", "true"))  // 自适应帧率
            optional.add(MediaConstraints.KeyValuePair("googAdaptiveResolution", "true")) // 自适应分辨率
            optional.add(MediaConstraints.KeyValuePair("googAdaptiveBitrate", "true"))    // 自适应码率

            // === REMB (Receiver Estimated Maximum Bitrate) ===
            optional.add(MediaConstraints.KeyValuePair("googRemb", "true"))               // 启用REMB
            optional.add(MediaConstraints.KeyValuePair("googRembOnConnect", "true"))      // 连接时启用REMB

            // 码率控制范围 - 1.5Mbps最小保证
            optional.add(MediaConstraints.KeyValuePair("googMinBitrate", "1500"))         // 最小1.5Mbps

            // 质量保护配置 - 针对高带宽优化
            optional.add(MediaConstraints.KeyValuePair("googCpuOveruseDetection", "true"))
            optional.add(MediaConstraints.KeyValuePair("googEnableVideoSuspendBelowMinBitrate", "false"))
            optional.add(MediaConstraints.KeyValuePair("googHighStartBitrate", "true"))
            optional.add(MediaConstraints.KeyValuePair("googBitrateAdjustmentUp", "1.3"))     // 上调系数1.3倍
            optional.add(MediaConstraints.KeyValuePair("googBitrateAdjustmentDown", "0.9"))   // 下调系数0.9倍
            optional.add(MediaConstraints.KeyValuePair("googBandwidthLimitedResolution", "false")) // 不限制分辨率

            // 最小质量保护 - 480P 15帧 (更灵活的下限)
            optional.add(MediaConstraints.KeyValuePair("minWidth", MIN_VIDEO_WIDTH.toString()))
            optional.add(MediaConstraints.KeyValuePair("minHeight", MIN_VIDEO_HEIGHT.toString()))
            optional.add(MediaConstraints.KeyValuePair("minFrameRate", MIN_VIDEO_FPS.toString()))

            // 额外的自适应优化
            optional.add(MediaConstraints.KeyValuePair("googPayloadPadding", "true"))     // 负载填充
            optional.add(MediaConstraints.KeyValuePair("googScreencastMinBitrate", "400")) // 最小屏幕共享码率
        }

        Log.d(TAG, "🌐 应用WebRTC核心自适应算法:")
        Log.d(TAG, "   🚦 GCC (Google Congestion Control): ✅ 启用 + 激进探测")
        Log.d(TAG, "   📊 BWE (Bandwidth Estimation): ✅ 启用 + 快速上升")
        Log.d(TAG, "   🔄 Adaptive Bitrate Control: ✅ 启用")
        Log.d(TAG, "   📡 REMB (Receiver Estimated Maximum Bitrate): ✅ 启用")
        Log.d(TAG, "   📦 缓冲区: 视频${VIDEO_BUFFER_FRAMES}帧, 音频${AUDIO_BUFFER_FRAMES}帧")
        Log.d(TAG, "   📊 码率范围: 最小3Mbps, 无上限")
        Log.d(TAG, "   📺 最小质量: ${MIN_VIDEO_WIDTH}x${MIN_VIDEO_HEIGHT}@${MIN_VIDEO_FPS}fps (480P)")
        Log.d(TAG, "   ⚡ 拥塞控制: 上调1.3x, 下调0.9x, 快速探测")

        peerConnection?.createOffer(object : SdpObserver {
            override fun onCreateSuccess(sessionDescription: SessionDescription?) {
                sessionDescription?.let {
                    // 分析offer SDP中的候选项
                    analyzeOfferSDP(it)

                    peerConnection?.setLocalDescription(object : SdpObserver {
                        override fun onSetSuccess() {
                            Log.d(TAG, "📝 本地SDP设置成功，等待ICE收集完成...")
                            // 不立即发送，等待ICE收集完成
                        }
                        override fun onSetFailure(error: String?) {
                            Log.e(TAG, "设置本地描述失败: $error")
                        }
                        override fun onCreateSuccess(p0: SessionDescription?) {}
                        override fun onCreateFailure(p0: String?) {}
                    }, it)
                }
            }
            
            override fun onCreateFailure(error: String?) {
                Log.e(TAG, "创建Offer失败: $error")
                runOnUiThread {
                    Toast.makeText(this@MainActivity, "创建Offer失败", Toast.LENGTH_SHORT).show()
                    binding.statusText.text = "状态: Offer创建失败"
                    binding.startButton.isEnabled = true
                    binding.stopButton.isEnabled = false
                }
                // 重置状态
                isStreaming = false
                peerConnection?.close()
                peerConnection = null
            }
            
            override fun onSetSuccess() {}
            override fun onSetFailure(error: String?) {}
        }, mediaConstraints)
    }
    
    private fun sendOffer(sessionDescription: SessionDescription) {
        val serverUrl = binding.serverUrlInput.text.toString().trim()
        
        if (binding.whipRadio.isChecked) {
            sendWhipOffer(serverUrl, sessionDescription)
        } else {
            sendSrsWebRTCOffer(serverUrl, sessionDescription)
        }
    }
    
    private fun sendWhipOffer(url: String, sessionDescription: SessionDescription) {
        lifecycleScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    val requestBody = sessionDescription.description
                        .toRequestBody("application/sdp".toMediaType())
                    
                    val request = Request.Builder()
                        .url(url)
                        .post(requestBody)
                        .header("Content-Type", "application/sdp")
                        .build()
                    
                    val response = okHttpClient.newCall(request).execute()
                    
                    if (response.isSuccessful) {
                        val answerSdp = response.body?.string() ?: ""
                        val answer = SessionDescription(SessionDescription.Type.ANSWER, answerSdp)
                        
                        peerConnection?.setRemoteDescription(object : SdpObserver {
                            override fun onSetSuccess() {
                                Log.d(TAG, "WHIP推流设置成功")
                            }
                            override fun onSetFailure(error: String?) {
                                Log.e(TAG, "设置远程描述失败: $error")
                            }
                            override fun onCreateSuccess(p0: SessionDescription?) {}
                            override fun onCreateFailure(p0: String?) {}
                        }, answer)
                    } else {
                        runOnUiThread {
                            Toast.makeText(this@MainActivity, "WHIP连接失败: ${response.code}", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "WHIP连接异常", e)
                runOnUiThread {
                    Toast.makeText(this@MainActivity, "WHIP连接异常: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    private fun sendSrsWebRTCOffer(serverUrl: String, sessionDescription: SessionDescription) {
        // 解析 webrtc://host/app/stream 格式
        val (host, app, stream) = if (serverUrl.startsWith("webrtc://")) {
            val urlParts = serverUrl.replace("webrtc://", "").split("/")
            val hostPart = urlParts.getOrNull(0) ?: "localhost"
            val appPart = urlParts.getOrNull(1) ?: "live"
            val streamPart = urlParts.getOrNull(2) ?: "livestream"
            Triple(hostPart, appPart, streamPart)
        } else {
            Triple("localhost", "live", "livestream")
        }
        
        val signallingUrl = "http://$host:1985/rtc/v1/publish/"
        val webrtcUrl = "webrtc://$host/$app/$stream"
        
        lifecycleScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    val jsonRequest = JSONObject().apply {
                        put("api", signallingUrl)
                        put("streamurl", webrtcUrl)
                        put("sdp", sessionDescription.description)
                    }
                    
                    val requestBody = jsonRequest.toString()
                        .toRequestBody("application/json".toMediaType())
                    
                    val request = Request.Builder()
                        .url(signallingUrl)
                        .post(requestBody)
                        .header("Content-Type", "application/json")
                        .build()
                    
                    val response = okHttpClient.newCall(request).execute()
                    
                    if (response.isSuccessful) {
                        val responseBody = response.body?.string()
                        val jsonResponse = JSONObject(responseBody ?: "{}")
                        
                        val code = jsonResponse.optInt("code", -1)
                        if (code == 0) {
                            val sdpAnswer = jsonResponse.optString("sdp", "")
                            if (sdpAnswer.isNotEmpty()) {
                                val answer = SessionDescription(SessionDescription.Type.ANSWER, sdpAnswer)
                                
                                peerConnection?.setRemoteDescription(object : SdpObserver {
                                    override fun onSetSuccess() {
                                        Log.d(TAG, "SRS WebRTC推流设置成功")
                                    }
                                    override fun onSetFailure(error: String?) {
                                        Log.e(TAG, "设置远程描述失败: $error")
                                    }
                                    override fun onCreateSuccess(p0: SessionDescription?) {}
                                    override fun onCreateFailure(p0: String?) {}
                                }, answer)
                            }
                        } else {
                            runOnUiThread {
                                Toast.makeText(this@MainActivity, "SRS连接失败: code=$code", Toast.LENGTH_SHORT).show()
                            }
                        }
                    } else {
                        runOnUiThread {
                            Toast.makeText(this@MainActivity, "SRS连接失败: ${response.code}", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "SRS连接异常", e)
                runOnUiThread {
                    Toast.makeText(this@MainActivity, "SRS连接异常: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    private fun stopStreaming() {
        if (!isStreaming) return
        
        Log.d(TAG, "开始停止推流（保持摄像头预览）...")
        
        isStreaming = false
        
        try {
            // 注意：不停止摄像头采集，保持本地预览
            // 只关闭网络推流连接
            
            // 从PeerConnection中移除轨道，但保持本地预览
            peerConnection?.senders?.forEach { sender ->
                peerConnection?.removeTrack(sender)
            }
            Log.d(TAG, "已从PeerConnection移除轨道")
            
            // 关闭PeerConnection（网络连接）
            peerConnection?.close()
            peerConnection = null
            Log.d(TAG, "PeerConnection已关闭")
            
            // 确保本地轨道继续启用以保持预览
            localVideoTrack?.setEnabled(true)
            localAudioTrack?.setEnabled(true)
            Log.d(TAG, "本地预览继续运行")
            
            // 更新UI状态
            runOnUiThread {
                binding.statusText.text = "状态: 已停止"
                binding.startButton.isEnabled = true
                binding.stopButton.isEnabled = false
            }
            
            Log.d(TAG, "推流已完全停止")
            
        } catch (e: Exception) {
            Log.e(TAG, "停止推流时出错: ${e.message}")
            runOnUiThread {
                binding.statusText.text = "状态: 停止时出错"
            }
        }
    }
    
    private fun switchCamera() {
        val cameraVideoCapturer = videoCapturer as? CameraVideoCapturer
        cameraVideoCapturer?.switchCamera(object : CameraVideoCapturer.CameraSwitchHandler {
            override fun onCameraSwitchDone(frontCamera: Boolean) {
                isFrontCamera = frontCamera
                runOnUiThread {
                    binding.localVideoView.setMirror(frontCamera)
                    Toast.makeText(this@MainActivity, 
                        "已切换到${if (frontCamera) "前置" else "后置"}摄像头", 
                        Toast.LENGTH_SHORT).show()
                }
                Log.d(TAG, "摄像头切换完成: ${if (frontCamera) "前置" else "后置"}")
            }
            
            override fun onCameraSwitchError(errorDescription: String?) {
                Log.e(TAG, "摄像头切换失败: $errorDescription")
                runOnUiThread {
                    Toast.makeText(this@MainActivity, "摄像头切换失败", Toast.LENGTH_SHORT).show()
                }
            }
        })
    }
    
    override fun onDestroy() {
        super.onDestroy()
        
        // 清理资源
        localVideoTrack?.removeSink(binding.localVideoView)
        videoCapturer?.stopCapture()
        videoCapturer?.dispose()
        
        peerConnection?.close()
        peerConnectionFactory?.dispose()
        
        binding.localVideoView.release()
        eglBase?.release()
        
        Log.d(TAG, "资源清理完成")
    }

    private fun resetIceStatistics() {
        iceGatheringStartTime = 0L
        totalCandidatesCollected = 0
        hostCandidates = 0
        stunCandidates = 0
        turnCandidates = 0
        otherCandidates = 0
        offerSent = false
        Log.d(TAG, "🔄 ICE候选项统计已重置")
    }

    private fun sendOfferWithCandidates() {
        if (offerSent) {
            Log.d(TAG, "📤 SDP已发送，跳过重复发送")
            return
        }

        peerConnection?.localDescription?.let { localDesc ->
            Log.d(TAG, "🚀 发送包含ICE候选项的SDP...")
            Log.d(TAG, "   📊 当前已收集候选项: $totalCandidatesCollected 个")

            // 重新分析SDP确认候选项已包含
            analyzeOfferSDP(localDesc)

            offerSent = true
            sendOffer(localDesc)
        } ?: run {
            Log.e(TAG, "❌ 无法获取本地SDP描述")
        }
    }

    private fun analyzeOfferSDP(sessionDescription: SessionDescription) {
        val sdp = sessionDescription.description
        val candidateLines = sdp.split('\n').filter { it.startsWith("a=candidate:") }

        Log.d(TAG, "📄 SDP分析: ${sessionDescription.type}, ${candidateLines.size}个候选项")

        if (candidateLines.isEmpty()) {
            Log.w(TAG, "⚠️ SDP中没有候选项")
        }
    }
}