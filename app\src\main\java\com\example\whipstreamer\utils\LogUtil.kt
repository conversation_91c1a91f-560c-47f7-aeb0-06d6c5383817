package com.example.whipstreamer.utils

import android.util.Log
import com.example.whipstreamer.BuildConfig

/**
 * 日志工具类
 * 在 Release 版本中自动禁用日志输出
 */
object LogUtil {
    
    // 日志开关，通过 BuildConfig 控制
    private val isLogEnabled = BuildConfig.DEBUG
    
    // 默认 TAG
    private const val DEFAULT_TAG = "WhipStreamer"
    
    /**
     * Verbose 级别日志
     */
    @JvmStatic
    fun v(tag: String = DEFAULT_TAG, msg: String) {
        if (isLogEnabled) {
            Log.v(tag, msg)
        }
    }
    
    /**
     * Debug 级别日志
     */
    @JvmStatic
    fun d(tag: String = DEFAULT_TAG, msg: String) {
        if (isLogEnabled) {
            Log.d(tag, msg)
        }
    }
    
    /**
     * Info 级别日志
     */
    @JvmStatic
    fun i(tag: String = DEFAULT_TAG, msg: String) {
        if (isLogEnabled) {
            Log.i(tag, msg)
        }
    }
    
    /**
     * Warning 级别日志
     */
    @JvmStatic
    fun w(tag: String = DEFAULT_TAG, msg: String) {
        if (isLogEnabled) {
            Log.w(tag, msg)
        }
    }
    
    /**
     * Warning 级别日志（带异常）
     */
    @JvmStatic
    fun w(tag: String = DEFAULT_TAG, msg: String, throwable: Throwable) {
        if (isLogEnabled) {
            Log.w(tag, msg, throwable)
        }
    }
    
    /**
     * Error 级别日志
     */
    @JvmStatic
    fun e(tag: String = DEFAULT_TAG, msg: String) {
        if (isLogEnabled) {
            Log.e(tag, msg)
        }
    }
    
    /**
     * Error 级别日志（带异常）
     */
    @JvmStatic
    fun e(tag: String = DEFAULT_TAG, msg: String, throwable: Throwable) {
        if (isLogEnabled) {
            Log.e(tag, msg, throwable)
        }
    }
    
    /**
     * 格式化日志输出
     */
    @JvmStatic
    fun format(tag: String = DEFAULT_TAG, format: String, vararg args: Any?) {
        if (isLogEnabled) {
            Log.d(tag, String.format(format, *args))
        }
    }
    
    /**
     * 输出方法调用栈
     */
    @JvmStatic
    fun printStackTrace(tag: String = DEFAULT_TAG) {
        if (isLogEnabled) {
            val stackTrace = Thread.currentThread().stackTrace
            val sb = StringBuilder()
            sb.append("Call Stack:\n")
            for (i in 3 until stackTrace.size) {
                sb.append("\tat ${stackTrace[i]}\n")
            }
            Log.d(tag, sb.toString())
        }
    }
}