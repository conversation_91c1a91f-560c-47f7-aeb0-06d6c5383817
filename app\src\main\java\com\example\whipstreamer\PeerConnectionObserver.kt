package com.example.whipstreamer

import org.webrtc.*

/**
 * WebRTC PeerConnection 观察者基类
 * 兼容WebRTC SDK 104.5112.09版本的所有接口要求
 */
open class PeerConnectionObserver : PeerConnection.Observer {
    
    override fun onSignalingChange(signalingState: PeerConnection.SignalingState?) {
        // 默认空实现
    }

    override fun onIceConnectionChange(iceConnectionState: PeerConnection.IceConnectionState?) {
        // 默认空实现
    }

    override fun onIceConnectionReceivingChange(p0: Boolean) {
        // WebRTC SDK要求的接口 - 参数名为p0
    }

    override fun onIceGatheringChange(iceGatheringState: PeerConnection.IceGatheringState?) {
        // 默认空实现
    }

    override fun onIceCandidate(iceCandidate: IceCandidate?) {
        // 默认空实现
    }

    override fun onIceCandidatesRemoved(iceCandidates: Array<out IceCandidate>?) {
        // 默认空实现
    }

    override fun onAddStream(mediaStream: MediaStream?) {
        // 默认空实现 - 注意：这个方法在新版本中已废弃，但仍需实现
    }

    override fun onRemoveStream(mediaStream: MediaStream?) {
        // 默认空实现 - 注意：这个方法在新版本中已废弃，但仍需实现
    }

    override fun onDataChannel(dataChannel: DataChannel?) {
        // 默认空实现
    }

    override fun onRenegotiationNeeded() {
        // 默认空实现
    }

    override fun onAddTrack(receiver: RtpReceiver?, mediaStreams: Array<out MediaStream>?) {
        // 默认空实现
    }

    override fun onConnectionChange(newState: PeerConnection.PeerConnectionState?) {
        // 默认空实现
    }

    override fun onSelectedCandidatePairChanged(event: CandidatePairChangeEvent?) {
        // 默认空实现 - WebRTC新版本要求的方法
    }
}